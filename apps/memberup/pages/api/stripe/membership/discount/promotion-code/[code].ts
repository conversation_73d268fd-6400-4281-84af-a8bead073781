import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPromotionCodes } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { code } = req.query
    console.log('Fetching membership promotion code:', code)

    const user = req['user']
    const stripeConnectAccount = user.membership_setting?.stripe_connect_account

    if (!stripeConnectAccount) {
      return res.status(200).send({
        success: false,
        data: [],
        message: 'Stripe connect account not found',
      })
    }

    const result = await stripeGetPromotionCodes(stripeConnectAccount, {
      code: code as string,
    })

    console.log('Membership promotion code result:', JSON.stringify(result.data))

    // Check if we found any promotion codes
    if (result.data && result.data.length > 0) {
      return res.status(200).send({
        success: true,
        data: result.data,
      })
    } else {
      // No promotion codes found with this code
      return res.status(200).send({
        success: false,
        data: [],
        message: 'No valid promotion code found',
      })
    }
  } catch (err: any) {
    console.log('Error fetching promotion code:', err)
    res.status(400).json({
      success: false,
      message: err.message,
      data: [],
    })
  }
})

export default handler
